package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*SupplyInfo)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*SupplyInfo)(nil))
}

// 商品物流、工厂信息
type SupplyInfo struct {
	ID                  uint      `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
	ProductId           uint      `json:"product_id"            gorm:"unique;not null"` //关联商品id
	PackSize            string    `json:"pack_size"`                                    //包装尺寸  string 长宽高顺序排列|分割
	ShipAddress         string    `json:"ship_address"`                                 //发货地址
	PackWeight          string    `json:"pack_weight"`                                  //包装重量
	RealWeight          string    `json:"real_weight"`                                  //真实重量  单位kg
	ProduceFactory      string    `json:"produce_factory"`                              //生产工厂id
	PurchaseFactoryId   string    `json:"purchase_factory_id"`                          //采购工厂id
	PurchaseFactoryName string    `json:"purchase_factory_name"`                        //采购工厂名
	ProduceFactoryName  string    `json:"produce_factory_name"`                         //生产工厂名
}

func (s *SupplyInfo) TableName() string {
	return "supply_info"
}

// 新增
func (s *SupplyInfo) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(s.TableName()).Create(s).Error
}

// 更新先不存在就弄一个
func (s *SupplyInfo) UpdateSupplyInfo(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if db.Model(s).Where("product_id = ?", s.ProductId).Updates(s).RowsAffected == 0 {
		return db.Create(s).Error
	}
	return db.Error
}

// 根据 product id 查询
func (s *SupplyInfo) QueryByProductId(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	db = db.Model(s)
	// 直接复制处理，如果漏了传 ProducId 就直接查不到
	db = db.Where("product_id = ?", s.ProductId)
	return db.First(s).Error
}

func (c *SupplyInfo) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*SupplyInfo, 0)

	err := db.Find(&list).Error

	return list, err
}

func (s *SupplyInfo) DeleteByIds(ids []uint, tx ...interface{}) (err error) {
	db := mysql.NewConnV2()

	if len(tx) != 0 {
		db = tx[0].(*gorm2.DB)
	}
	err = db.Model(&SupplyInfo{}).Where("id IN (?)", ids).Delete(s).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *SupplyInfo) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *SupplyInfo) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(SupplyInfo)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *SupplyInfo) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
