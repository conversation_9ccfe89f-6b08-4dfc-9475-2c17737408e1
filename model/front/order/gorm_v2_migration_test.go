package order

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestGormV2Migration 测试 GORM v2 迁移
func TestGormV2Migration(t *testing.T) {
	// 跳过需要数据库连接的测试，专注于方法签名验证
	t.Skip("跳过数据库连接测试，专注于编译和方法签名验证")

	t.Run("测试Create方法", func(t *testing.T) {
		testCreateMethod(t)
	})

	t.Run("测试Update方法", func(t *testing.T) {
		testUpdateMethod(t)
	})

	t.Run("测试QueryByOrderCode方法", func(t *testing.T) {
		testQueryByOrderCodeMethod(t)
	})

	t.Run("测试GetOrderInfoByOrderCode方法", func(t *testing.T) {
		testGetOrderInfoByOrderCodeMethod(t)
	})
}

func testCreateMethod(t *testing.T) {
	orderInfo := &OrderInfo{
		UserID:    12345,
		OrderCode: "TEST-GORM-V2-CREATE-001",
		PriceInfo: PriceInfo{
			TotalMoney: 2599,
		},
		StateInfo: StateInfo{
			OrderState: NOT_PAY,
		},
		OrderType: POD_ORDER,
		ShopType:  ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// 测试创建
	err := orderInfo.Create()
	assert.NoError(t, err, "Create方法应该成功")
	assert.NotZero(t, orderInfo.ID, "创建后应该有ID")

	// 清理测试数据
	defer func() {
		err := orderInfo.DeleteOrder()
		if err != nil {
			t.Logf("清理测试数据失败: %v", err)
		}
	}()
}

func testUpdateMethod(t *testing.T) {
	// 先创建一个测试订单
	orderInfo := &OrderInfo{
		UserID:    12345,
		OrderCode: "TEST-GORM-V2-UPDATE-001",
		PriceInfo: PriceInfo{
			TotalMoney: 2599,
		},
		StateInfo: StateInfo{
			OrderState: NOT_PAY,
		},
		OrderType: POD_ORDER,
		ShopType:  ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := orderInfo.Create()
	assert.NoError(t, err, "创建测试订单应该成功")

	// 更新订单
	orderInfo.TotalMoney = 3599
	orderInfo.OrderState = HAVE_PAY
	err = orderInfo.Update()
	assert.NoError(t, err, "Update方法应该成功")

	// 验证更新结果
	queryOrder := &OrderInfo{OrderCode: orderInfo.OrderCode}
	err = queryOrder.QueryByOrderCode()
	assert.NoError(t, err, "查询更新后的订单应该成功")
	assert.Equal(t, int32(3599), queryOrder.TotalMoney, "总金额应该已更新")
	assert.Equal(t, HAVE_PAY, queryOrder.OrderState, "订单状态应该已更新")

	// 清理测试数据
	defer func() {
		err := orderInfo.DeleteOrder()
		if err != nil {
			t.Logf("清理测试数据失败: %v", err)
		}
	}()
}

func testQueryByOrderCodeMethod(t *testing.T) {
	// 先创建一个测试订单
	orderInfo := &OrderInfo{
		UserID:    12345,
		OrderCode: "TEST-GORM-V2-QUERY-001",
		PriceInfo: PriceInfo{
			TotalMoney: 2599,
		},
		StateInfo: StateInfo{
			OrderState: NOT_PAY,
		},
		OrderType: POD_ORDER,
		ShopType:  ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := orderInfo.Create()
	assert.NoError(t, err, "创建测试订单应该成功")

	// 测试查询
	queryOrder := &OrderInfo{OrderCode: orderInfo.OrderCode}
	err = queryOrder.QueryByOrderCode()
	assert.NoError(t, err, "QueryByOrderCode方法应该成功")
	assert.Equal(t, orderInfo.OrderCode, queryOrder.OrderCode, "订单号应该匹配")
	assert.Equal(t, orderInfo.UserID, queryOrder.UserID, "用户ID应该匹配")
	assert.Equal(t, orderInfo.TotalMoney, queryOrder.TotalMoney, "总金额应该匹配")

	// 清理测试数据
	defer func() {
		err := orderInfo.DeleteOrder()
		if err != nil {
			t.Logf("清理测试数据失败: %v", err)
		}
	}()
}

func testGetOrderInfoByOrderCodeMethod(t *testing.T) {
	// 先创建一个测试订单
	orderInfo := &OrderInfo{
		UserID:    12345,
		OrderCode: "TEST-GORM-V2-GETINFO-001",
		PriceInfo: PriceInfo{
			TotalMoney: 2599,
		},
		StateInfo: StateInfo{
			OrderState: NOT_PAY,
		},
		OrderType: POD_ORDER,
		ShopType:  ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err := orderInfo.Create()
	assert.NoError(t, err, "创建测试订单应该成功")

	// 测试查询（带预加载）
	queryOrder := &OrderInfo{OrderCode: orderInfo.OrderCode}
	err = queryOrder.GetOrderInfoByOrderCode()
	assert.NoError(t, err, "GetOrderInfoByOrderCode方法应该成功")
	assert.Equal(t, orderInfo.OrderCode, queryOrder.OrderCode, "订单号应该匹配")
	assert.Equal(t, orderInfo.UserID, queryOrder.UserID, "用户ID应该匹配")

	// 清理测试数据
	defer func() {
		err := orderInfo.DeleteOrder()
		if err != nil {
			t.Logf("清理测试数据失败: %v", err)
		}
	}()
}

// TestMethodSignaturesV2 测试 GORM v2 迁移后的方法签名兼容性
func TestMethodSignaturesV2(t *testing.T) {
	orderInfo := &OrderInfo{
		OrderCode: "TEST-SIGNATURE-V2-001",
		UserID:    12345,
	}

	// 测试方法签名是否保持兼容
	// 这些调用不会实际执行，只是检查编译是否通过

	// 测试Create方法签名（支持interface{}参数）
	var createFunc func(...interface{}) error = orderInfo.Create
	assert.NotNil(t, createFunc, "Create方法签名应该正确")

	// 测试Update方法签名（支持interface{}参数）
	var updateFunc func(...interface{}) error = orderInfo.Update
	assert.NotNil(t, updateFunc, "Update方法签名应该正确")

	// 测试QueryByOrderCode方法签名
	var queryFunc func() error = orderInfo.QueryByOrderCode
	assert.NotNil(t, queryFunc, "QueryByOrderCode方法签名应该正确")

	// 测试GetOrderInfoByOrderCode方法签名
	var getInfoFunc func(...string) error = orderInfo.GetOrderInfoByOrderCode
	assert.NotNil(t, getInfoFunc, "GetOrderInfoByOrderCode方法签名应该正确")
}

// TestGormTagsV2 测试 GORM v2 标签更新
func TestGormTagsV2(t *testing.T) {
	// 这个测试主要验证结构体标签是否正确更新
	// 实际的标签验证会在运行时通过数据库操作来验证
	
	orderInfo := &OrderInfo{}
	assert.NotNil(t, orderInfo, "OrderInfo结构体应该可以正常实例化")
	
	// 验证表名方法
	tableName := orderInfo.TableName()
	assert.Equal(t, "order_info", tableName, "表名应该正确")
}
