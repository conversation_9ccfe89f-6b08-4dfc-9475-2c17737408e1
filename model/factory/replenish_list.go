package factory

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func init() {
	mysql.RegisterTable((*ReplenishList)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*ReplenishList)(nil))
}

// 补货闭环 之 补货列表
type ReplenishList struct {
	ID           uint      `gorm:"PRIMARY_KEY" json:"id,omitempty"`
	CreatedAt    time.Time `json:"created_at,omitempty"`
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	ColorID      string    `json:"color_id,omitempty"`   //颜色id
	ColorName    string    `json:"color_name,omitempty"` //颜色名称
	SizeID       string    `json:"size_id,omitempty"`    //尺码id
	SizeName     string    `json:"size_name,omitempty"`  //尺码名称
	ProductCName string    `json:"product_c_name"`       //商品中文名
	Location     string    `json:"location"`

	Number      int    `json:"number"` // 补货数量 || 退货数量
	InventoryID uint   `json:"inventory_id"`
	StorageID   uint   `json:"storage_id,omitempty"`
	PurchaseID  uint   `json:"purchase_id"`                        //采购工厂ID
	FactoryName string `json:"factory_name"       gorm:"not null"` //工厂名`

	ArrangeTime int64  `form:"arrange_time" json:"arrange_time"` // 期望交期
	Remarks     string `form:"remarks" json:"remarks"`           // 备注

	FifteenDaySales        int     `json:"fifteen_day_sales"`        // 15天总销量 创建记录时的数值
	Percentage             float64 `json:"percentage"`               // 销量占比 创建记录时的数值
	PostReplenishInventory float64 `json:"post_replenish_inventory"` // 补货后库存天数 创建记录时的数值

	Inventory Inventory `json:"inventory"  gorm:"foreignkey:location;association_foreignkey:location"`
}

type ReplenishListItem struct {
	ID           uint      `gorm:"PRIMARY_KEY" json:"id,omitempty"`
	CreatedAt    time.Time `json:"created_at,omitempty"`
	UpdatedAt    time.Time `json:"updated_at,omitempty"`
	ColorID      string    `json:"color_id,omitempty"`   //颜色id
	ColorName    string    `json:"color_name,omitempty"` //颜色名称
	SizeID       string    `json:"size_id,omitempty"`    //尺码id
	SizeName     string    `json:"size_name,omitempty"`  //尺码名称
	ProductCName string    `json:"product_c_name"`       //商品中文名
	Location     string    `json:"location"`

	Number      int    `json:"number"` // 补货数量 || 退货数量
	InventoryID uint   `json:"inventory_id"`
	StorageID   uint   `json:"storage_id,omitempty"`
	PurchaseID  uint   `json:"purchase_id"`                        //采购工厂ID
	FactoryName string `json:"factory_name"       gorm:"not null"` //工厂名`

	ArrangeTime int64  `form:"arrange_time" json:"arrange_time"` // 期望交期
	Remarks     string `form:"remarks" json:"remarks"`           // 备注

	FifteenDaySales        int     `json:"fifteen_day_sales"`        // 15天总销量 创建记录时的数值
	Percentage             float64 `json:"percentage"`               // 销量占比 创建记录时的数值
	PostReplenishInventory float64 `json:"post_replenish_inventory"` // 补货后库存天数 创建记录时的数值

	Inventory    Inventory        `json:"-"  gorm:"foreignkey:location;association_foreignkey:location"`
	InventoryRed InventoryRedItem `json:"inventory"`
}

func (r *ReplenishList) TableName() string {
	return "replenish_list"
}

// 新增
func (r *ReplenishList) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(r.TableName()).Create(r).Error
}

// 获取列表
func (r *ReplenishList) GetList() (list []*ReplenishList, err error) {
	db := mysql.NewConn().Table(r.TableName()).Preload("Inventory")

	if r.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", r.PurchaseID)
	}

	if r.ColorName != "" {
		db = db.Where("color_name like ?", "%"+r.ColorName+"%")
	}

	if r.ProductCName != "" {
		db = db.Where("product_c_name like ?", "%"+r.ProductCName+"%")
	}

	if r.ColorID != "" {
		db = db.Where("color_id = ?", r.ColorID)
	}

	err = db.Find(&list).Error
	return
}

// 获取列表 另一个结构返回
func (r *ReplenishList) GetListItem() (list []*ReplenishListItem, err error) {
	db := mysql.NewConn().Table(r.TableName()).Preload("Inventory")

	if r.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", r.PurchaseID)
	}

	if r.ColorName != "" {
		db = db.Where("color_name like ?", "%"+r.ColorName+"%")
	}

	if r.ProductCName != "" {
		db = db.Where("product_c_name like ?", "%"+r.ProductCName+"%")
	}

	if r.ColorID != "" {
		db = db.Where("color_id = ?", r.ColorID)
	}

	err = db.Find(&list).Error
	return
}

type FactoryGroup struct {
	PurchaseID  uint   `json:"purchase_id"`                        //采购工厂ID
	FactoryName string `json:"factory_name"       gorm:"not null"` //工厂名
	Total       int    `json:"total"`
}

func (r *ReplenishList) GetFactoryGroup() (list []*FactoryGroup, err error) {
	err = mysql.NewConn().Table(r.TableName()).Select("count(*) as total,factory_name,purchase_id").Group("purchase_id").Scan(&list).Error
	return
}

func (r *ReplenishList) IsExists() (bool, error) {
	var count int = 0
	db := mysql.NewConn().Table(r.TableName())

	if r.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", r.PurchaseID)
	}

	db = db.Where("color_id = ?", r.ColorID).Count(&count)

	if db.Error == gorm.ErrRecordNotFound {
		return false, nil
	}
	if db.Error != nil {
		return false, db.Error
	}
	return count >= 1, nil

}

// PutReplenish is 弃用  妈的不生效可能用的不对
func (r *ReplenishList) PutReplenish(tx ...*gorm2.DB) (err error) {

	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "location"}},
		DoUpdates: clause.AssignmentColumns([]string{"number"}),
	}).Create(r).Error

	return
}

func (r *ReplenishList) UpdateNumber(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	if r.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", r.PurchaseID)
	}

	err := db.Model(&ReplenishList{}).Where("location = ?", r.Location).Update("number", r.Number).Error
	return err
}

func (r *ReplenishList) DeleteByColorID() error {
	db := mysql.NewConn().Table(r.TableName())

	if r.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", r.PurchaseID)
	}

	return db.Where("color_id = ?", r.ColorID).Delete(r).Error
}

func (r *ReplenishList) DeleteByPurchaseID(purchaseID uint, tx *gorm.DB) error {
	db := tx.Table(r.TableName())
	if r.ColorID != "" {
		db = db.Where("color_id = ?", r.ColorID)
	}

	return db.Where("purchase_id = ?", purchaseID).Delete(r).Error
}

func (c *ReplenishList) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*ReplenishList, 0)

	err := db.Find(&list).Error

	return list, err
}

func (r *ReplenishList) DeleteByIds(ids []uint, tx ...interface{}) (err error) {
	db := mysql.NewConnV2()

	if len(tx) != 0 {
		db = tx[0].(*gorm2.DB)
	}
	err = db.Model(&ReplenishList{}).Where("id IN (?)", ids).Delete(r).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *ReplenishList) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *ReplenishList) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(ReplenishList)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *ReplenishList) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

// 根据map 更新
func (c *ReplenishList) UpdateByMap(data map[string]interface{}, tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	if c.PurchaseID != 0 {
		db = db.Where("purchase_id = ?", c.PurchaseID)
	}

	if c.ColorID != "" {
		db = db.Where("color_id = ?", c.ColorID)
	}

	if c.Location != "" {
		db = db.Where("location = ?", c.Location)
	}

	return db.Model(&ReplenishList{}).Updates(data).Error

}

// GetLastPurchaseID 获取更新时间最近的一条记录的 purchase_id
func (c *ReplenishList) GetLastPurchaseID() (err error) {
	db := mysql.NewConn().Table(c.TableName()).Select("purchase_id").Order("updated_at desc").Where("color_id = ?", c.ColorID)
	db.First(c)
	return
}
