package design

import (
	"encoding/json"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*PackagingInfo)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	model.SyncRegister((*PackagingInfo)(nil))
}

type PackagingInfo struct {
	ID        uint      `gorm:"PRIMARY_KEY" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	//基础信息
	PackID              string `json:"pack_id" gorm:"unique;not null"` //包装ID  类似 N001
	Name                string `json:"name"`                           //包装名称
	State               int    `json:"state"  gorm:"default:1"`        // 1 待上架 2 上架  3 淘汰
	ProduceFactoryID    uint   `json:"produce_factory_id"`             //生产工厂id
	PurchaseFactoryID   uint   `json:"purchase_factory_id"`            //采购工厂id
	PurchaseFactoryName string `json:"purchase_factory_name"`          //采购工厂名
	ProduceFactoryName  string `json:"produce_factory_name"`           //生产工厂名
	Description         string `json:"description" gorm:"type:BLOB;"`  //整体描述
	Price               int    `json:"price"`                          // 包装定价 美分
	SingleSided         bool   `json:"single_sided"`                   // 是否单面  默认false 双面、true 单面

	// 与库存相关的字段
	Cname                string `json:"cname"`                    //包装中文名
	InventoryStorageName string `json:"inventory_storage_name"`   //库存的库位名称
	Quality              int    `json:"quality" gorm:"default:0"` //库存
	Threshold            int    `json:"threshold"`                //预警值
	//与设计相关
	DesignPiece string `json:"design_piece" gorm:"type:BLOB;"` // 设计区信息,预置写死 类型[]DesignPiece
	DesignModel string `json:"design_model" gorm:"type:BLOB;"` // 模特图片信息,除图片路径与对应设计区外信息写死 类型 []DesignModel
}

func (p *PackagingInfo) TableName() string {
	return "packaging_info"
}

// 获取工厂本地和 pod 端进行校验的数据，只有 id 和 更新时间
func (p *PackagingInfo) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*model.VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(p.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}

func (p *PackagingInfo) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	detailObj := new(PackagingInfo)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[detailObj.ID] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	}
	return

}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (p *PackagingInfo) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(p.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", p.ID).Updates(p).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", p.ID).Update("updated_at", p.UpdatedAt).Error

	return
}

func (p *PackagingInfo) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(p.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*PackagingInfo, 0)

	err := db.Find(&list).Error

	return list, err
}

func (p *PackagingInfo) DeleteByIds(ids []uint, tx ...interface{}) (err error) {
	db := mysql.NewConnV2()

	if len(tx) != 0 {
		db = tx[0].(*gorm2.DB)
	}
	err = db.Model(&PackagingInfo{}).Where("id IN (?)", ids).Delete(p).Error
	return
}

// 新增
func (p *PackagingInfo) Create(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(p.TableName()).Create(p).Error
}

func (p *PackagingInfo) Query() (err error) {
	return mysql.NewConn().Table(p.TableName()).Where("id = ?", p.ID).First(p).Error
}

func (p *PackagingInfo) UpdateDetails(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Model(p).Updates(p).First(p).Error
}

func (p *PackagingInfo) MinusQuality(tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(&PackagingInfo{}).Where("id = ?", p.ID).
		UpdateColumn("quality", gorm.Expr("quality - ?", 1)).
		First(p).Error
}

func (p *PackagingInfo) MinusQualityByCount(count int, tx ...*gorm.DB) error {

	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	return db.Model(&PackagingInfo{}).Where("id = ?", p.ID).
		UpdateColumn("quality", gorm.Expr("quality - ?", count)).Error
}

func (p *PackagingInfo) QueryList(pn, ps int) (list []*PackagingInfo, count int, err error) {

	db := mysql.NewConn().Table(p.TableName())

	if p.PackID != "" {
		db = db.Where("pack_id = ?", p.PackID)
	}
	if p.Name != "" {
		db = db.Where("name Like ?", "%"+p.Name+"%")
	}
	if p.State != 0 {
		db = db.Where("state = ?", p.State)
	}
	if p.InventoryStorageName != "" {
		db = db.Where("inventory_storage_name = ?", p.InventoryStorageName)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	err = db.Order("id desc").Offset((pn - 1) * ps).Limit(ps).Find(&list).Error

	return
}
