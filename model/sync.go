package model

import (
	"errors"
)

type VerifyHead struct {
	ID          uint  `json:"id"`
	UpdatedTime int64 `json:"updated_time"`
}

type SyncInterface interface {
	TableName() string
	GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error)
	CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error)
	GetDetailList(ids []uint) (list interface{}, err error)
	DeleteByIds(ids []uint, tx ...interface{}) (err error)
}

var syncRegistry = map[string]SyncInterface{}

func SyncRegister(handle SyncInterface) {
	syncRegistry[handle.TableName()] = handle
	return
}

func GetSyncHandle(tableName string) (handle SyncInterface, err error) {
	handle, has := syncRegistry[tableName]
	if !has {
		err = errors.New("Cannot get the sync handle for table " + tableName)
		return
	}
	return
}
