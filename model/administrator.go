package model

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"zx/unit/pkg/ecode"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/pkg/define"
	"zx/zxgo/log"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
)

func init() {
	mysql.RegisterTable((*Administrator)(nil), mysql.OPTION_USE_INNODB_ENGINE,
		mysql.OPTION_USE_UTF8mb4_CHARSET,
		mysql.OPTION_USE_UTF8mb4_GENERAL_CI_COLLATE)
	SyncRegister((*Administrator)(nil))

}

type Administrator struct {
	ID             int64     `gorm:"PRIMARY_KEY" json:"ID"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	Account        string    `json:"account"         gorm:"unique;not null;index"` // 登录账户
	Password       string    `json:"password"        gorm:"not null"`              // 密码
	Salt           string    `json:"salt"            gorm:"not null"`              // 盐（随机生产）
	Permission     uint64    `json:"permisson"`                                    // 权限，bit
	Name           string    `json:"name"`                                         // 姓名
	ProduceFactory int       `json:"produce_factory" gorm:"default:0"`             // 生产工厂id，目前直接使用【主键id】
	Sensitive      int       `json:"sensitive"       gorm:"default:1"`             // 敏感权限 2 为有权限
}

var QuerySQL string
var QueryVars []interface{}

// 创建、更新的后置函数
func (a *Administrator) AfterFind(tx *gorm.DB) error {

	// V1
	log.Debug("QuerySQL: ", QuerySQL)
	log.Debug("QueryVars: ", QueryVars)

	// v2
	//if tx.Error == nil {
	//	sql := tx.Statement.SQL.String()
	//	vars := tx.Statement.Vars
	//
	//	// 将SQL和参数传递给另一个函数执行
	//	log.Info("after save work order, sql: ", sql)
	//	log.Info("after save work order, vars: ", vars)
	//}

	return nil
}

var SaveSQL string
var SaveVars []interface{}

// 创建、更新的后置函数
func (a *Administrator) AfterSave(tx *gorm.DB) error {
	if tx.Error == nil {
		//sql := tx.Statement.SQL.String()
		//vars := tx.Statement.Vars

		// 将SQL和参数传递给另一个函数执行
		//log.Info("after save work order, sql: ", sql)
		//log.Info("after save work order, vars: ", vars)

		log.Debug("SaveSQL: ", SaveSQL)
		log.Debug("SaveVars: ", SaveVars)

		// 将数据推送到 kafka ，给印花厂消费

		//dataMsgKey := fmt.Sprintf("%s_%d_%s", zx_queue.Factory_Order_Kafka_TOPIC, zx_queue.OrderDeliveryRescan, orderCode)
		//dataInfo := data_push.DataInfo{
		//	OperationType: data_push.OrderCreate,
		//	Sql:           sql,
		//	Vars:          vars,
		//}
		//msgData, _ := json.Marshal(dataInfo)
		//
		//kafkaMsg := &kafkamq.Message{}
		//kafkaMsg.SetTopic(zx_queue.Factory_Order_Kafka_TOPIC)
		//kafkaMsg.SetGroupId("")
		//kafkaMsg.SetTags([]string{fmt.Sprintf("Order %s redelivery.", orderCode)})
		//kafkaMsg.SetData(msgData)
		//kafkaMsg.SetMessageId(dataMsgKey)
		//
		//err := data_push.ProduceManagePushToFactory(context.Background(), kafkaMsg)
		//if err != nil {
		//	log.Error(err)
		//	return err
		//}

		return nil
	}
	return nil
}

func (a *Administrator) BeforeSave(*gorm.DB) (err error) {

	log.Debug("00000000000000000000000: BeforeSave")
	//a.UpdatedAt = time.Now()

	return
}

func (a *Administrator) Update(tx ...*gorm.DB) (err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	err = db.Model(a).Where("account = ?", a.Account).Updates(map[string]string{
		"name": "123456",
	}).First(a).Error
	return
}

func (a *Administrator) TableName() string {
	return "administrator"
}

// 新增
func (a *Administrator) Create(tx ...*gorm2.DB) error {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}
	return db.Table(a.TableName()).Create(a).Error
}

// 查询
func (a *Administrator) Query(tx ...*gorm.DB) error {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Model(a)
	if len(a.Account) > 0 {
		db = db.Where("account = ?", a.Account)
	} else {
		return nil
	}

	return db.First(a).Error
}

type AdminInfo struct {
	ID   int64  `json:"id"`
	Name string `json:"name"` // 姓名
}

// GetList 根据Name 模糊查询列表，列表返回值只有ID和Name
func (a *Administrator) GetList(tx ...*gorm.DB) (list []*AdminInfo, err error) {
	db := mysql.NewConn()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(a.TableName())
	if len(a.Name) > 0 {
		db = db.Where("name like ?", "%"+a.Name+"%")
	}

	err = db.Find(&list).Error
	return
}

func (c *Administrator) GetDetailList(ids []uint) (interface{}, error) {

	db := mysql.NewConn().Table(c.TableName())

	db = db.Where("id IN (?)", ids)

	list := make([]*Administrator, 0)

	err := db.Find(&list).Error

	return list, err
}

func (a *Administrator) DeleteByIds(ids []uint, tx ...interface{}) (err error) {
	db := mysql.NewConnV2()

	if len(tx) != 0 {
		db = tx[0].(*gorm2.DB)
	}
	err = db.Model(&Administrator{}).Where("id IN (?)", ids).Delete(a).Error
	return
}

// ！！！工厂本地专用，因为工厂本地的数据要喝 pod 后端保持一致，不需要更新时间为当前时间
func (c *Administrator) UpdateWithZero(tx ...*gorm2.DB) (err error) {
	db := mysql.NewConnV2()
	if len(tx) != 0 {
		db = tx[0]
	}

	db = db.Table(c.TableName())

	// ！！！这里，这里的更新不能使用 Model，不然的话，
	err = db.Session(&gorm2.Session{DisableNestedTransaction: true}).Select("*").Omit("updated_at").Where("id = ?", c.ID).Updates(c).Error
	if err != nil {
		return
	}

	err = db.Where("id = ?", c.ID).Update("updated_at", c.UpdatedAt).Error

	return
}

func (c *Administrator) CreateOrUpdate(detail interface{}, idMapType map[uint]int) (err error) {
	detailData, err := json.Marshal(detail)
	if err != nil {
		log.Error(err)
		return
	}

	log.Debug("detailData: ", string(detailData))

	detailObj := new(Administrator)
	err = json.Unmarshal(detailData, detailObj)
	if err != nil {
		log.Error(err)
		err = ecode.WithCode(err, define.JSON_ERR, define.CodeMsgName)
		return
	}

	switch idMapType[uint(detailObj.ID)] {
	case 1:
		err = detailObj.Create()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	case 2:
		err = detailObj.UpdateWithZero()
		if err != nil {
			log.Error(err)
			err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
			return
		}
	default:
		err = errors.New(fmt.Sprintf("Sync type error, id: %d", detailObj.ID))
		log.Error(err)
		err = ecode.WithCode(err, define.DB_ACCESS_ERROR, define.CodeMsgName)
		return
	}
	return

}

func (c *Administrator) GetVerifyHead(pn, ps int, idMin, idMax uint, updateTimeS, updateTimeE int64) (list []*VerifyHead, count int64, err error) {

	db := mysql.NewConn().Table(c.TableName()).Select("id, UNIX_TIMESTAMP(updated_at) as updated_time")

	if idMax > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id <= ?", idMax)
	}

	if idMin > 0 {
		// 工厂本地查本地 mysql 的校验 head 时 id 的值会大于0
		db = db.Where("id >= ?", idMin)
	}

	if updateTimeS > 0 {
		db = db.Where("updated_at >= FROM_UNIXTIME(?)", updateTimeS)
	}
	if updateTimeE > 0 {
		db = db.Where("updated_at <= FROM_UNIXTIME(?)", updateTimeE)
	}

	err = db.Count(&count).Error
	if err != nil {
		return
	}

	if idMax < 1 && idMin < 1 {
		db = db.Offset((pn - 1) * ps).Limit(ps)
	}

	err = db.Find(&list).Error

	return
}
