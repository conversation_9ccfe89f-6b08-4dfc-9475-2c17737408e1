package shopify_graphql

import (
	"encoding/json"
	"fmt"
	"testing"
	"zx/unit/pkg/mysql"
	"zx/zx-consistency/model/front/order"
	model "zx/zx-consistency/model/front/user"
)

func InitMysql() {
	var mysqlCfgTest = mysql.Config{
		Addr:         "mysql-test-20240228.cuhd7h3xijsl.us-east-2.rds.amazonaws.com:3306",
		Username:     "admin",
		Password:     "00056ef19b84a86c03a3c5df81a62c6dbed",
		Database:     "pod_partner",
		ShowSQL:      true,
		MaxIdleInter: 2,
	}
	if err := mysql.Init(&mysqlCfgTest); err != nil {
		panic(">>>>>>>> err: " + err.Error())
	}
}

func TestCreateFulfillment(t *testing.T) {

	InitMysql()

	associatedShopQueryDB := &model.AssociatedShop{
		ShopName: "linxb-2024-12-20.myshopify.com",
	}
	err := associatedShopQueryDB.Query()
	if err != nil {
		t.Error(err)
		return
	}

	orderInfoQueryDB := &order.OrderInfo{
		OrderCode: "T2025010111825263",
	}
	err = orderInfoQueryDB.QueryByOrderCode()
	if err != nil {
		t.Error(err)
		return
	}

	client := ClientWithToken(associatedShopQueryDB.Token, associatedShopQueryDB.GetSimpleShopifyShopName())

	// 先获取订单详情，然后取到 fulfillment_order
	shopifyOrderDetail, err := OrderDetail(client, orderInfoQueryDB.ThirdOrderCode)
	if err != nil {
		panic(err)
	}
	jsonStr, _ := json.Marshal(shopifyOrderDetail)
	fmt.Println("shopifyOrderDetail: ", string(jsonStr))

	// 根据 fulfillment_order 创建 fulfillment
	err = CreateFulfillment(client, orderInfoQueryDB, shopifyOrderDetail)
	if err != nil {
		t.Error(err)
		return
	}
}

func TestAcceptFulfillment(t *testing.T) {
	InitMysql()

	associatedShopQueryDB := &model.AssociatedShop{
		ShopName: "linxb-2024-12-20.myshopify.com",
	}
	err := associatedShopQueryDB.Query()
	if err != nil {
		t.Error(err)
		return
	}

	orderInfoQueryDB := &order.OrderInfo{
		OrderCode: "T2025010158625739",
	}
	err = orderInfoQueryDB.QueryByOrderCode()
	if err != nil {
		t.Error(err)
		return
	}

	client := ClientWithToken(associatedShopQueryDB.Token, associatedShopQueryDB.GetSimpleShopifyShopName())

	// 先获取订单详情，然后取到 fulfillment_order
	shopifyOrderDetail, err := OrderDetail(client, orderInfoQueryDB.ThirdOrderCode)
	if err != nil {
		panic(err)
	}
	jsonStr, _ := json.Marshal(shopifyOrderDetail)
	fmt.Println("shopifyOrderDetail: ", string(jsonStr))

	err = AcceptFulfillment(client, shopifyOrderDetail, "")
	if err != nil {
		t.Error(err)
		return
	}

}

func TestRegisterFulfillmentService(t *testing.T) {
	InitMysql()

	associatedShopQueryDB := &model.AssociatedShop{
		ShopName: "linxb-2024-12-20.myshopify.com",
	}
	err := associatedShopQueryDB.Query()
	if err != nil {
		t.Error(err)
		return
	}

	client := ClientWithToken("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", "linxb-2024-12-20")

	fulfillmentServiceResult, err := RegisterFulfillmentService(client, "PODpartner", "https://www.podpartner.com/fulfillment_service_callback")
	if err != nil {
		t.Error(err)
		return
	}

	fulfillmentServiceResultData, _ := json.Marshal(fulfillmentServiceResult)
	t.Log("fulfillmentServiceResult: ", string(fulfillmentServiceResultData))

	return
}
