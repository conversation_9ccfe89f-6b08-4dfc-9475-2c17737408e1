package main

import (
	"database/sql"
	"fmt"
	"reflect"
	"unsafe"

	"github.com/jinzhu/gorm"
	gorm2 "gorm.io/gorm"
	"gorm.io/driver/mysql"
)

// TransactionConverter GORM v1 到 v2 事务转换器
type TransactionConverter struct{}

// ConvertV1ToV2Transaction 将 GORM v1 事务转换为 v2 事务
func (tc *TransactionConverter) ConvertV1ToV2Transaction(v1Tx *gorm.DB) (*gorm2.DB, error) {
	// 方案一：通过反射获取底层 sql.Tx
	v2Tx, err := tc.convertViaReflection(v1Tx)
	if err == nil {
		return v2Tx, nil
	}

	// 方案二：通过 CommonDB 接口转换
	v2Tx, err = tc.convertViaCommonDB(v1Tx)
	if err == nil {
		return v2Tx, nil
	}

	// 方案三：通过连接字符串重新创建（最后的选择）
	return tc.convertViaConnectionString(v1Tx)
}

// convertViaReflection 通过反射获取底层 sql.Tx
func (tc *TransactionConverter) convertViaReflection(v1Tx *gorm.DB) (*gorm2.DB, error) {
	// 获取 GORM v1 的底层数据库连接
	v1Value := reflect.ValueOf(v1Tx).Elem()
	
	// 查找 db 字段（GORM v1 内部的数据库连接）
	dbField := v1Value.FieldByName("db")
	if !dbField.IsValid() {
		return nil, fmt.Errorf("无法找到 GORM v1 的 db 字段")
	}

	// 使用 unsafe 包访问私有字段
	dbFieldPtr := unsafe.Pointer(dbField.UnsafeAddr())
	sqlDB := (*sql.DB)(dbFieldPtr)

	if sqlDB == nil {
		return nil, fmt.Errorf("GORM v1 的底层数据库连接为空")
	}

	// 创建 GORM v2 连接
	dialector := mysql.New(mysql.Config{
		Conn: sqlDB,
	})

	v2DB, err := gorm2.Open(dialector, &gorm2.Config{})
	if err != nil {
		return nil, fmt.Errorf("创建 GORM v2 连接失败: %v", err)
	}

	// 如果 v1 是事务，需要在 v2 中也开始事务
	if v1Tx.GetErrors() == nil && tc.isTransaction(v1Tx) {
		return v2DB.Begin(), nil
	}

	return v2DB, nil
}

// convertViaCommonDB 通过 CommonDB 接口转换
func (tc *TransactionConverter) convertViaCommonDB(v1Tx *gorm.DB) (*gorm2.DB, error) {
	// 获取 GORM v1 的 CommonDB 接口
	commonDB := v1Tx.CommonDB()
	if commonDB == nil {
		return nil, fmt.Errorf("无法获取 GORM v1 的 CommonDB")
	}

	// 尝试将 CommonDB 转换为 sql.DB 或 sql.Tx
	switch db := commonDB.(type) {
	case *sql.DB:
		// 如果是 sql.DB，创建新的 GORM v2 连接
		dialector := mysql.New(mysql.Config{
			Conn: db,
		})
		return gorm2.Open(dialector, &gorm2.Config{})

	case *sql.Tx:
		// 如果是 sql.Tx，需要特殊处理
		// 这种情况比较复杂，因为我们需要在同一个事务中创建 GORM v2
		return tc.convertSqlTxToGormV2(db)

	default:
		return nil, fmt.Errorf("不支持的 CommonDB 类型: %T", commonDB)
	}
}

// convertSqlTxToGormV2 将 sql.Tx 转换为 GORM v2 事务
func (tc *TransactionConverter) convertSqlTxToGormV2(sqlTx *sql.Tx) (*gorm2.DB, error) {
	// 这是最复杂的部分，需要从 sql.Tx 创建 GORM v2 事务
	// 由于 GORM v2 不直接支持从 sql.Tx 创建，我们需要一些技巧

	// 方案：使用 sql.Tx 的连接信息
	// 注意：这种方法可能不完全可靠，因为我们无法直接从 sql.Tx 获取原始连接字符串
	return nil, fmt.Errorf("从 sql.Tx 转换到 GORM v2 事务暂不支持")
}

// convertViaConnectionString 通过连接字符串重新创建
func (tc *TransactionConverter) convertViaConnectionString(v1Tx *gorm.DB) (*gorm2.DB, error) {
	// 这种方法会创建新的连接，无法保持事务一致性
	// 仅作为最后的备选方案
	return nil, fmt.Errorf("通过连接字符串转换会破坏事务一致性，不推荐使用")
}

// isTransaction 检查 GORM v1 连接是否是事务
func (tc *TransactionConverter) isTransaction(db *gorm.DB) bool {
	// 检查是否有事务相关的标记
	// GORM v1 中，事务通常会设置特定的标记
	
	// 方法1：检查是否调用过 Begin()
	if db.Error != nil {
		return false
	}

	// 方法2：通过反射检查内部状态
	v := reflect.ValueOf(db).Elem()
	
	// 查找事务相关字段
	if field := v.FieldByName("inTransaction"); field.IsValid() {
		return field.Bool()
	}

	// 方法3：检查 CommonDB 类型
	commonDB := db.CommonDB()
	_, isTx := commonDB.(*sql.Tx)
	return isTx
}

// 使用示例和测试
func demonstrateTransactionConversion() {
	fmt.Println("🔄 演示 GORM v1 到 v2 事务转换...")

	converter := &TransactionConverter{}

	// 模拟场景：有一个 GORM v1 事务
	fmt.Println("\n📋 转换方案分析:")

	fmt.Println("✅ 方案一：反射获取底层连接")
	fmt.Println("   - 优点：可以获取真实的底层连接")
	fmt.Println("   - 缺点：使用 unsafe 包，可能不稳定")
	fmt.Println("   - 适用：开发和测试环境")

	fmt.Println("✅ 方案二：CommonDB 接口转换")
	fmt.Println("   - 优点：使用公开接口，相对安全")
	fmt.Println("   - 缺点：对于 sql.Tx 转换复杂")
	fmt.Println("   - 适用：大部分场景")

	fmt.Println("❌ 方案三：连接字符串重建")
	fmt.Println("   - 优点：简单直接")
	fmt.Println("   - 缺点：破坏事务一致性")
	fmt.Println("   - 适用：非事务场景")

	fmt.Println("\n⚠️  重要限制:")
	fmt.Println("1. 反射方法依赖 GORM v1 内部实现，可能不稳定")
	fmt.Println("2. sql.Tx 到 GORM v2 的转换技术上困难")
	fmt.Println("3. 即使转换成功，也可能存在兼容性问题")

	fmt.Println("\n🎯 推荐策略:")
	fmt.Println("1. 优先使用双版本方法（CreateV2 + Create）")
	fmt.Println("2. 渐进式迁移：逐步将事务升级到 v2")
	fmt.Println("3. 避免在生产环境使用复杂的转换逻辑")
}

// 实际的转换实现（简化版）
func ConvertGormV1ToV2Transaction(v1Tx *gorm.DB) (*gorm2.DB, error) {
	// 检查是否为空
	if v1Tx == nil {
		return nil, fmt.Errorf("GORM v1 事务为空")
	}

	// 获取底层连接
	commonDB := v1Tx.CommonDB()
	if commonDB == nil {
		return nil, fmt.Errorf("无法获取底层数据库连接")
	}

	// 根据类型进行转换
	switch db := commonDB.(type) {
	case *sql.DB:
		// 非事务连接，可以转换
		dialector := mysql.New(mysql.Config{
			Conn: db,
		})
		v2DB, err := gorm2.Open(dialector, &gorm2.Config{})
		if err != nil {
			return nil, fmt.Errorf("创建 GORM v2 连接失败: %v", err)
		}
		return v2DB, nil

	case *sql.Tx:
		// 事务连接，转换复杂，建议返回错误
		return nil, fmt.Errorf("GORM v1 事务转换为 v2 事务技术上困难，建议使用 CreateV2() 方法")

	default:
		return nil, fmt.Errorf("不支持的数据库连接类型: %T", commonDB)
	}
}

func main() {
	demonstrateTransactionConversion()

	fmt.Println("\n🔧 实际使用建议:")
	fmt.Println("虽然技术上可以转换，但推荐使用更安全的方案：")
	fmt.Println("1. 使用双版本方法（CreateV2 + Create）")
	fmt.Println("2. 在调用层面升级事务到 GORM v2")
	fmt.Println("3. 避免复杂的运行时转换逻辑")
}
