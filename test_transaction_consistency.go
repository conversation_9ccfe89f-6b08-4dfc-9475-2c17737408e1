package main

import (
	"fmt"
	"log"
	"time"

	"zx/zx-consistency/model/front/order"
	gorm2 "gorm.io/gorm"
)

func main() {
	fmt.Println("🔍 测试事务一致性问题...")
	
	// 测试方法签名兼容性
	testMethodSignatures()
	
	// 演示事务一致性问题
	demonstrateTransactionIssue()
	
	// 展示正确的解决方案
	demonstrateCorrectSolution()
	
	fmt.Println("✅ 事务一致性测试完成")
}

func testMethodSignatures() {
	fmt.Println("\n📋 测试新的方法签名...")
	
	orderInfo := &order.OrderInfo{}
	
	// 测试 CreateV2 方法签名
	var createV2Func func(...*gorm2.DB) error = orderInfo.CreateV2
	if createV2Func == nil {
		log.Fatal("❌ CreateV2 方法签名错误")
	}
	fmt.Println("✅ CreateV2(tx ...*gorm2.DB) error - 签名正确")
	
	// 测试 UpdateV2 方法签名
	var updateV2Func func(...*gorm2.DB) error = orderInfo.UpdateV2
	if updateV2Func == nil {
		log.Fatal("❌ UpdateV2 方法签名错误")
	}
	fmt.Println("✅ UpdateV2(tx ...*gorm2.DB) error - 签名正确")
	
	// 测试兼容版本的 Create 方法签名
	var createFunc func(...interface{}) error = orderInfo.Create
	if createFunc == nil {
		log.Fatal("❌ Create 方法签名错误")
	}
	fmt.Println("✅ Create(tx ...interface{}) error - 签名正确（兼容版本）")
	
	// 测试兼容版本的 Update 方法签名
	var updateFunc func(...interface{}) error = orderInfo.Update
	if updateFunc == nil {
		log.Fatal("❌ Update 方法签名错误")
	}
	fmt.Println("✅ Update(tx ...interface{}) error - 签名正确（兼容版本）")
}

func demonstrateTransactionIssue() {
	fmt.Println("\n⚠️  演示事务一致性问题...")
	
	fmt.Println("问题场景：")
	fmt.Println("1. 调用方使用 GORM v1 事务")
	fmt.Println("2. OrderInfo.Create() 内部创建新的 GORM v2 连接")
	fmt.Println("3. 订单和订单子项在不同事务中执行")
	fmt.Println("4. 可能导致：订单创建成功，订单子项创建失败")
	
	// 模拟问题代码
	fmt.Println("\n❌ 有问题的代码模式：")
	fmt.Println(`
func CreateOrderWithItems(orderInfo *OrderInfo, items []*OrderItem) error {
    // GORM v1 事务
    tx := mysql.NewConn().Begin()
    
    // 这里会创建新的 v2 连接，脱离当前事务！
    if err := orderInfo.Create(tx); err != nil {
        tx.Rollback()
        return err
    }
    
    // 订单子项在原事务中创建
    for _, item := range items {
        if err := item.Create(tx); err != nil {
            tx.Rollback()  // 只能回滚订单子项，订单已经提交！
            return err
        }
    }
    
    return tx.Commit().Error
}`)
	
	fmt.Println("\n💥 风险：")
	fmt.Println("- 订单在独立连接中创建（自动提交）")
	fmt.Println("- 订单子项在事务中创建")
	fmt.Println("- 如果订单子项失败，订单无法回滚")
	fmt.Println("- 导致数据不一致")
}

func demonstrateCorrectSolution() {
	fmt.Println("\n✅ 正确的解决方案...")
	
	fmt.Println("方案一：使用 V2 方法（推荐）")
	fmt.Println(`
func CreateOrderWithItems(orderInfo *OrderInfo, items []*OrderItem) error {
    // 使用 GORM v2 事务
    tx := mysql.NewConnV2().Begin()
    
    // 使用 CreateV2，确保在同一事务中
    if err := orderInfo.CreateV2(tx); err != nil {
        tx.Rollback()
        return err
    }
    
    // 订单子项也在同一事务中
    for _, item := range items {
        if err := item.CreateV2(tx); err != nil {
            tx.Rollback()  // 可以正确回滚所有操作
            return err
        }
    }
    
    return tx.Commit().Error
}`)
	
	fmt.Println("\n方案二：渐进式迁移")
	fmt.Println(`
// 第一步：升级事务到 v2，保持接口不变
func CreateOrderWithItems(orderInfo *OrderInfo, items []*OrderItem) error {
    // 将事务升级为 v2
    tx := mysql.NewConnV2().Begin()  // 改这里
    
    // 使用 V2 方法
    if err := orderInfo.CreateV2(tx); err != nil {  // 改这里
        tx.Rollback()
        return err
    }
    
    for _, item := range items {
        if err := item.CreateV2(tx); err != nil {  // 改这里
            tx.Rollback()
            return err
        }
    }
    
    return tx.Commit().Error
}`)
	
	fmt.Println("\n🎯 关键改进：")
	fmt.Println("✅ 所有操作在同一个 GORM v2 事务中")
	fmt.Println("✅ 保证原子性：要么全部成功，要么全部失败")
	fmt.Println("✅ 正确的错误处理和回滚机制")
	fmt.Println("✅ 数据一致性得到保障")
}

// 演示正确的事务使用模式
func demonstrateTransactionPattern() {
	fmt.Println("\n🔧 推荐的事务使用模式...")
	
	orderInfo := &order.OrderInfo{
		UserID:    12345,
		OrderCode: "DEMO-TX-001",
		PriceInfo: order.PriceInfo{
			TotalMoney: 2599,
		},
		StateInfo: order.StateInfo{
			OrderState: order.NOT_PAY,
		},
		OrderType: order.POD_ORDER,
		ShopType:  order.ORDER_SHOP_TYPE_SHOPIFY,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	
	fmt.Println("✅ 推荐模式 1：使用 V2 方法")
	fmt.Printf("   orderInfo.CreateV2()  // 无事务\n")
	fmt.Printf("   orderInfo.CreateV2(tx)  // 有事务\n")
	
	fmt.Println("✅ 推荐模式 2：兼容模式（有警告）")
	fmt.Printf("   orderInfo.Create()  // 会记录警告日志\n")
	
	fmt.Println("⚠️  注意事项：")
	fmt.Println("   - 优先使用 V2 方法")
	fmt.Println("   - 确保事务类型一致")
	fmt.Println("   - 关注警告日志")
	fmt.Println("   - 测试事务回滚行为")
}
