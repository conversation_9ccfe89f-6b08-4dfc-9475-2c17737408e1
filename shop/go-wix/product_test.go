package go_wix

import (
	"testing"
)

var testClient *Client

func TestMain(m *testing.M) {

	/* 生产：
			ApiKey:       "1501871e-bed6-4c84-8d1b-86110493145e",
			ApiSecret:    "164a1106-aa62-4fa3-9f79-0e1792e318f8",
	测试：
	ApiKey: 	f7175701-c7d2-4551-8c09-5c62cde7816e
	ApiSecret: 	23183491-6cbd-45d7-bf25-d14a4f0773c5
	*/

	app := WixApp{
		ApiKey:       "1501871e-bed6-4c84-8d1b-86110493145e",
		ApiSecret:    "164a1106-aa62-4fa3-9f79-0e1792e318f8",
		RedirectUrl:  "",
		RefreshToken: "OAUTH2.eyJraWQiOiJkZ0x3cjNRMCIsImFsZyI6IkhTMjU2In0.eyJkYXRhIjoie1wiaWRcIjpcImNkNzMzZGZiLWY5MmQtNDI0ZC05MmUzLWFjNTI4NDNkM2E4NVwifSIsImlhdCI6MTY2MzIwNjQ4MSwiZXhwIjoxNzI2Mjc4NDgxfQ.0YjDJb0PogTcLqbNmVUwTTFbBElS-K01pRZJ-BMQF_Q",
	}

	//tokenObj, err := RefreshToken(app.ApiKey, app.ApiSecret, app.RefreshToken)
	//if err != nil {
	//	//fmt.Println("err: ", err)
	//	return
	//}

	// 这里使用 postman 去刷新 token，因为每次执行测试用例都刷新 token 的话，会出现 wix 访问不到的问题，可能 wix 那边做了限制了
	token := "OAUTH2.eyJraWQiOiJLaUp3NXZpeSIsImFsZyI6IlJTMjU2In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cLm7q8csp5MOkXy7q7r6Cg5ivB8Jb2BPKCaI4TmAf8rq8hl3Hf-Rdb0XoEWxgLHeNntqOOj_KvQrKAP-nX-JfEl6obKqJsuBL-AZXGO1yhPZl7zYsxRn5bZ2KsYPpKCBeYWDu3uT3PqZwJ83NAfuW6krhvGDJs0qkvHuXxD157r9RN3ooh0NXhw58aab2iHcE2OMXAS7EiraJb2v0fSj4iLCjPkbXOZkmojJV4PNk5_lo1OqSYT1ERds1lyxQkgkOfDcQOssId-oM3_v1sxgvZw3hL_t82tqX7Y6LOSXAFz6CyCIPxFnAQwHtv7YD3qxFCelB1zBMAahrosCtpZm1Q"

	testClient = NewClient(app /*tokenObj.AccessToken*/, token, app.RefreshToken)

	m.Run()
}

func TestProductServiceOp_Create(t *testing.T) {

	product := ProductShell{
		Product: Product{
			Name:        "PODpartner Test product 03",
			ProductType: "physical",
			PriceData: &Price{
				Price: float64(11.22),
			},
			Description: "This is a test from PODpartner Team.",
			//Sku:         "001U002",
			Visible: true,
			Ribbon:  "PODpartner Ribbon",
			Brand:   "PODpartner Brand",
			Weight:  0.19,
			Discount: &ProductDiscount{
				Type:  "amount",
				Value: 1,
			},
			ManageVariants: true,
			ProductOptions: []*ProductOptions{
				{
					Name: "Color",
					Choices: []*ProductChoices{
						{
							Value:       "white",
							Description: "white",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black",
							Description: "black",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black01",
							Description: "black01",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black02",
							Description: "black02",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black03",
							Description: "black03",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black04",
							Description: "black04",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black05",
							Description: "black05",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black06",
							Description: "black06",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black07",
							Description: "black07",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black08",
							Description: "black08",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black09",
							Description: "black09",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "black10",
							Description: "black10",
							InStock:     true,
							Visible:     true,
						},
					},
				},
				{
					Name: "Size",
					Choices: []*ProductChoices{
						{
							Value:       "L",
							Description: "L",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "XL",
							Description: "XL",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "XL01",
							Description: "XL01",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "XL02",
							Description: "XL02",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "XL03",
							Description: "XL03",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "XL04",
							Description: "XL04",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "XL05",
							Description: "XL05",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "XL06",
							Description: "XL06",
							InStock:     true,
							Visible:     true,
						},
						{
							Value:       "XL07",
							Description: "XL07",
							InStock:     true,
							Visible:     true,
						},
					},
				},
			},
		},
	}
	retProduct, err := testClient.Product.Create(&product)
	if err != nil {
		t.Error(err)
		return
	}

	t.Logf("ret product: %+v \n", *retProduct)
}

func TestProductServiceOp_List(t *testing.T) {
	query := ProductQuery{
		Query: &QueryProductFilter{
			Paging: &QueryProductFilterPaging{
				Limit:  100,
				Offset: 0,
			},
			//Filter: ,
			//Sort: ,
		},
		IncludeVariants:       true,
		IncludeHiddenProducts: true,
	}
	ret, err := testClient.Product.List(&query)
	if err != nil {
		t.Error(err)
		return
	}
	t.Logf("%+v", *ret)

	for _, v := range ret.Products {
		t.Log("instance id: ", v.ID)
		//t.Logf("MainMedia: %+v", *v.Media.MainMedia)
		t.Logf("MainMedia.Image: %+v", *v.Media.MainMedia.Image)
		//t.Log("MainMedia.Video: ", *v.Media.MainMedia.Video)
		//t.Logf("MainMedia.Thumbnail: %+v", *v.Media.MainMedia.Thumbnail)
		for i := 0; i < len(v.Media.Items); i++ {
			t.Logf("item %d, data: %+v", i, *v.Media.Items[i])
			t.Logf("item %d, data.Image: %+v", i, *v.Media.Items[i].Image)
			//t.Log("item ", i, ", data.Video: ", *v.Media.Items[i].Video)
			t.Logf("item %d, data.Thumbnail: %+v", i, *v.Media.Items[i].Thumbnail)
		}
		t.Log("======================================")
	}
}

func TestProductServiceOp_UpdateProductVariants(t *testing.T) {

	updateVariantShell := UpdateVariantShell{
		Variants: []*UpdateVariants{
			{
				Choices: &PodChoices{
					Size:  "L",
					Color: "white",
				},
				Price: 8.00,
				//Weight: 0.24,
				Sku:     "110U001Y1FM03",
				Visible: true,
			},
			{
				Choices: &PodChoices{
					Size:  "XL",
					Color: "white",
				},
				Price: 9.00,
				//Weight: 0.24,
				Sku:     "110U001Y1FM04",
				Visible: true,
			},
			{
				Choices: &PodChoices{
					Size:  "L",
					Color: "black",
				},
				Price: 8.00,
				//Weight: 0.24,
				Sku:     "110U001Y20M03",
				Visible: true,
			},
			{
				Choices: &PodChoices{
					Size:  "XL",
					Color: "black",
				},
				Price: 9.00,
				//Weight: 0.24,
				Sku:     "110U001Y20M04",
				Visible: true,
			},
		},
	}

	ret, err := testClient.Product.UpdateProductVariants(&updateVariantShell, "a353c57d-d02f-476b-9ae6-ccd1b75e1a1c")
	if err != nil {
		t.Error(err)
		return
	}
	t.Logf("ret: %+v", *ret)
}

func TestProductServiceOp_UpdateProductMedia(t *testing.T) {
	updateProductMedia := UpdateMediaShell{
		Media: []*UpdateMedia{
			{
				URL: "https://img.podpartner.com/product/images/84332d3e-e317-4efe-b78c-8244a3671e68.jpg",
			},
			{
				URL: "https://img.podpartner.com/product/images/4ca15800-c763-4fce-bdb3-0dee3b3be1b9.jpg",
			},
			{
				URL: "https://img.podpartner.com/product/images/45986667-512f-42ee-b6fc-3dd90db4aa44.jpg",
				Choice: &Choice{
					Option: "Color",
					Choice: "black",
				},
			},
			{
				URL: "https://img.podpartner.com/product/images/4ffc68b3-8803-491f-b2dd-12972a8c697d.jpg",
				Choice: &Choice{
					Option: "Color",
					Choice: "white",
				},
			},
		},
	}

	err := testClient.Product.UpdateProductMedia(&updateProductMedia, "b0025b9e-9317-4156-9543-62f29311b3cd")
	if err != nil {
		t.Error(err)
		return
	}
	t.Log("Success update media.")
}

func TestProductServiceOp_RemoveProductMedia(t *testing.T) {
	mediaIds := &RemoveMedia{
		MediaIds: []string{
			//"f18994_c890c84c2e2d4a9e9c95641316fe7dd8~mv2.jpg",
			//"f18994_887643bd6b9243989524cef6994fe5e0~mv2.jpg",
			//"f18994_bc673c84110a48378b223763076d1714~mv2.jpg",
		},
	}

	err := testClient.Product.RemoveProductMedia(mediaIds, "b0025b9e-9317-4156-9543-62f29311b3cd")
	if err != nil {
		t.Error(err)
		return
	}

	t.Log("success remove medias.")
}

func TestProductServiceOp_Update(t *testing.T) {
	updateProduct := &UpdateProductShell{
		Product: UpdateProduct{
			Name:        "Update PODpartner Test product 02.",
			Description: "Update descripion for PODpartner test product.",
		},
	}

	productId := "b0025b9e-9317-4156-9543-62f29311b3cd"
	ret, err := testClient.Product.Update(updateProduct, productId)
	if err != nil {
		t.Error(err)
		return
	}

	t.Log("ret: ", *ret)
}

func TestProductServiceOp_GetProduct(t *testing.T) {

	instanceId := "ba1960b1-18c9-4a1a-839c-c93a92c5b820"
	ret, err := testClient.Product.GetProduct(instanceId)
	if err != nil {
		t.Error(err)
		return
	}

	t.Log("instance id: ", ret.Product.ID)
	//t.Logf("MainMedia: %+v", *v.Media.MainMedia)
	t.Logf("MainMedia.Image: %+v", *ret.Product.Media.MainMedia.Image)
	//t.Log("MainMedia.Video: ", *v.Media.MainMedia.Video)
	//t.Logf("MainMedia.Thumbnail: %+v", *v.Media.MainMedia.Thumbnail)
	for i := 0; i < len(ret.Product.Media.Items); i++ {
		t.Logf("item %d, data: %+v", i, *ret.Product.Media.Items[i])
		t.Logf("item %d, data.Image: %+v", i, *ret.Product.Media.Items[i].Image)
		//t.Log("item ", i, ", data.Video: ", *v.Media.Items[i].Video)
		t.Logf("item %d, data.Thumbnail: %+v", i, *ret.Product.Media.Items[i].Thumbnail)
	}

	t.Log("====================================================================")
	// 获取到所有的选项，然后根据颜色进行区分，存到 shop_product_image 中
	for _, option := range ret.Product.ProductOptions {
		if option.Name == "Color" {
			for _, choice := range option.Choices {
				t.Logf("choice.Value: %s, len: %d, media: %+v", choice.Value, len(choice.Media.Items), choice.Media.Items[0])
			}
		}
	}

}
