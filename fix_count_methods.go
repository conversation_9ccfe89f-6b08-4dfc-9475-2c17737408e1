package main

import (
	"fmt"
	"io/ioutil"
	"regexp"
	"strings"
)

func main() {
	// 读取文件
	content, err := ioutil.ReadFile("model/front/order/order_info.go")
	if err != nil {
		panic(err)
	}

	fileContent := string(content)

	// 修复 Count 方法的类型问题
	// 1. 修复 countInfo 字段的 Count 调用
	patterns := []struct {
		pattern     string
		replacement string
	}{
		// 修复 countInfo.AllCount
		{
			pattern:     `err = db\.Count\(&countInfo\.AllCount\)\.Error`,
			replacement: `var allCount int64; err = db.Count(&allCount).Error; if err == nil { countInfo.AllCount = int(allCount) }`,
		},
		// 修复 countInfo.GOODSOUTOFSYNCCount
		{
			pattern:     `err = db\.Where\("order_state = \?", GOODS_OUT_OF_SYNC\)\.Count\(&countInfo\.GOODSOUTOFSYNCCount\)\.Error`,
			replacement: `var goodsOutOfSyncCount int64; err = db.Where("order_state = ?", GOODS_OUT_OF_SYNC).Count(&goodsOutOfSyncCount).Error; if err == nil { countInfo.GOODSOUTOFSYNCCount = int(goodsOutOfSyncCount) }`,
		},
		// 修复 countInfo.UNDELIVERABLEADDRESSCount
		{
			pattern:     `err = db\.Where\("order_state = \?", UNDELIVERABLE_ADDRESS\)\.Count\(&countInfo\.UNDELIVERABLEADDRESSCount\)\.Error`,
			replacement: `var undeliverableAddressCount int64; err = db.Where("order_state = ?", UNDELIVERABLE_ADDRESS).Count(&undeliverableAddressCount).Error; if err == nil { countInfo.UNDELIVERABLEADDRESSCount = int(undeliverableAddressCount) }`,
		},
		// 修复 countInfo.NOT_PAYCount
		{
			pattern:     `err = db\.Where\("order_state = \?", NOT_PAY\)\.Where\("all_custom_count = \?", 0\)\.Count\(&countInfo\.NOT_PAYCount\)\.Error`,
			replacement: `var notPayCount int64; err = db.Where("order_state = ?", NOT_PAY).Where("all_custom_count = ?", 0).Count(&notPayCount).Error; if err == nil { countInfo.NOT_PAYCount = int(notPayCount) }`,
		},
		// 修复 countInfo.HAVE_PAYCount
		{
			pattern:     `err = db\.Where\("order_state = \?", HAVE_PAY\)\.Count\(&countInfo\.HAVE_PAYCount\)\.Error`,
			replacement: `var havePayCount int64; err = db.Where("order_state = ?", HAVE_PAY).Count(&havePayCount).Error; if err == nil { countInfo.HAVE_PAYCount = int(havePayCount) }`,
		},
		// 修复 countInfo.HAVE_LOGISTICSCount
		{
			pattern:     `err = db\.Where\("order_state = \?", HAVE_WAYBILL\)\.Count\(&countInfo\.HAVE_LOGISTICSCount\)\.Error`,
			replacement: `var haveLogisticsCount int64; err = db.Where("order_state = ?", HAVE_WAYBILL).Count(&haveLogisticsCount).Error; if err == nil { countInfo.HAVE_LOGISTICSCount = int(haveLogisticsCount) }`,
		},
		// 修复 countInfo.PROBLEM_STATECount
		{
			pattern:     `err = db\.Where\("order_state = \?", PROBLEM_STATE\)\.Count\(&countInfo\.PROBLEM_STATECount\)\.Error`,
			replacement: `var problemStateCount int64; err = db.Where("order_state = ?", PROBLEM_STATE).Count(&problemStateCount).Error; if err == nil { countInfo.PROBLEM_STATECount = int(problemStateCount) }`,
		},
		// 修复 countInfo.IN_PRODUCTINGCount
		{
			pattern:     `err = db\.Where\("order_state = \? OR order_state = \?", IN_PRODUCTING, HAVE_WORK\)\.Count\(&countInfo\.IN_PRODUCTINGCount\)\.Error`,
			replacement: `var inProductingCount int64; err = db.Where("order_state = ? OR order_state = ?", IN_PRODUCTING, HAVE_WORK).Count(&inProductingCount).Error; if err == nil { countInfo.IN_PRODUCTINGCount = int(inProductingCount) }`,
		},
		// 修复 countInfo.PRODUCTING_OKCount
		{
			pattern:     `err = db\.Where\("order_state = \?", PRODUCTING_OK\)\.Count\(&countInfo\.PRODUCTING_OKCount\)\.Error`,
			replacement: `var productingOkCount int64; err = db.Where("order_state = ?", PRODUCTING_OK).Count(&productingOkCount).Error; if err == nil { countInfo.PRODUCTING_OKCount = int(productingOkCount) }`,
		},
		// 修复 countInfo.TESTING_OKCount
		{
			pattern:     `err = db\.Where\("order_state = \?", TESTING_OK\)\.Count\(&countInfo\.TESTING_OKCount\)\.Error`,
			replacement: `var testingOkCount int64; err = db.Where("order_state = ?", TESTING_OK).Count(&testingOkCount).Error; if err == nil { countInfo.TESTING_OKCount = int(testingOkCount) }`,
		},
		// 修复 countInfo.CANCELEDCount
		{
			pattern:     `err = db\.Where\("order_state = \?", CANCELED\)\.Count\(&countInfo\.CANCELEDCount\)\.Error`,
			replacement: `var canceledCount int64; err = db.Where("order_state = ?", CANCELED).Count(&canceledCount).Error; if err == nil { countInfo.CANCELEDCount = int(canceledCount) }`,
		},
		// 修复 countInfo.COMPLETEDCount
		{
			pattern:     `err = db\.Where\("order_state = \?", COMPLETED\)\.Count\(&countInfo\.COMPLETEDCount\)\.Error`,
			replacement: `var completedCount int64; err = db.Where("order_state = ?", COMPLETED).Count(&completedCount).Error; if err == nil { countInfo.COMPLETEDCount = int(completedCount) }`,
		},
		// 修复 countInfo.RefundCount
		{
			pattern:     `err = db\.Where\("order_state = \?", REFUNDED\)\.Count\(&countInfo\.RefundCount\)\.Error`,
			replacement: `var refundCount int64; err = db.Where("order_state = ?", REFUNDED).Count(&refundCount).Error; if err == nil { countInfo.RefundCount = int(refundCount) }`,
		},
		// 修复 countInfo.DELIVEREDCount
		{
			pattern:     `err = db\.Where\("order_state = \?", DELIVERED\)\.Count\(&countInfo\.DELIVEREDCount\)\.Error`,
			replacement: `var deliveredCount int64; err = db.Where("order_state = ?", DELIVERED).Count(&deliveredCount).Error; if err == nil { countInfo.DELIVEREDCount = int(deliveredCount) }`,
		},
		// 修复 countInfo.CustomCount
		{
			pattern:     `err = db\.Where\("order_state = \?", NOT_PAY\)\.Where\("all_custom_count > \?", 0\)\.Count\(&countInfo\.CustomCount\)\.Error`,
			replacement: `var customCount int64; err = db.Where("order_state = ?", NOT_PAY).Where("all_custom_count > ?", 0).Count(&customCount).Error; if err == nil { countInfo.CustomCount = int(customCount) }`,
		},
	}

	// 应用所有替换
	for _, p := range patterns {
		re := regexp.MustCompile(p.pattern)
		fileContent = re.ReplaceAllString(fileContent, p.replacement)
	}

	// 修复其他简单的 Count 调用
	// 修复 &count 为 int 类型的情况
	simplePatterns := []struct {
		pattern     string
		replacement string
	}{
		{
			pattern:     `err = db\.Count\(&count\)\.Error`,
			replacement: `var countInt64 int64; err = db.Count(&countInt64).Error; if err == nil { count = int(countInt64) }`,
		},
	}

	for _, p := range simplePatterns {
		re := regexp.MustCompile(p.pattern)
		fileContent = re.ReplaceAllString(fileContent, p.replacement)
	}

	// 写回文件
	err = ioutil.WriteFile("model/front/order/order_info.go", []byte(fileContent), 0644)
	if err != nil {
		panic(err)
	}

	fmt.Println("Count 方法修复完成")
}
