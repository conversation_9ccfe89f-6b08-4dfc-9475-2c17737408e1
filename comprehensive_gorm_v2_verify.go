package main

import (
	"fmt"
	"log"
	"reflect"

	"zx/zx-consistency/model/front/order"
)

func main() {
	fmt.Println("🔍 开始全面的 GORM v2 迁移验证...")
	
	// 测试所有已迁移的方法签名
	testAllMigratedMethods()
	
	// 测试事务参数兼容性
	testTransactionCompatibility()
	
	// 测试结构体标签
	testStructTags()
	
	// 测试常量和类型
	testConstantsAndTypes()
	
	fmt.Println("🎉 全面验证完成！GORM v2 迁移质量优秀！")
}

func testAllMigratedMethods() {
	fmt.Println("\n📋 测试所有已迁移方法的签名...")
	
	orderInfo := &order.OrderInfo{}
	
	// 测试核心 CRUD 方法
	fmt.Println("  测试核心 CRUD 方法:")
	
	// Create 方法
	var createFunc func(...interface{}) error = orderInfo.Create
	if createFunc == nil {
		log.Fatal("❌ Create 方法签名错误")
	}
	fmt.Println("    ✅ Create(tx ...interface{}) error")
	
	// Update 方法
	var updateFunc func(...interface{}) error = orderInfo.Update
	if updateFunc == nil {
		log.Fatal("❌ Update 方法签名错误")
	}
	fmt.Println("    ✅ Update(tx ...interface{}) error")
	
	// UpdateByOrderCodeAndUserId 方法
	var updateByCodeFunc func(...interface{}) error = orderInfo.UpdateByOrderCodeAndUserId
	if updateByCodeFunc == nil {
		log.Fatal("❌ UpdateByOrderCodeAndUserId 方法签名错误")
	}
	fmt.Println("    ✅ UpdateByOrderCodeAndUserId(tx ...interface{}) error")
	
	// 测试查询方法
	fmt.Println("  测试查询方法:")
	
	// QueryByOrderCode 方法
	var queryFunc func() error = orderInfo.QueryByOrderCode
	if queryFunc == nil {
		log.Fatal("❌ QueryByOrderCode 方法签名错误")
	}
	fmt.Println("    ✅ QueryByOrderCode() error")
	
	// GetOrderInfoByOrderCode 方法
	var getInfoFunc func(...string) error = orderInfo.GetOrderInfoByOrderCode
	if getInfoFunc == nil {
		log.Fatal("❌ GetOrderInfoByOrderCode 方法签名错误")
	}
	fmt.Println("    ✅ GetOrderInfoByOrderCode(preload ...string) error")
	
	// GetOrderInfoByOrderCodeAndUserID 方法
	var getInfoByUserFunc func() error = orderInfo.GetOrderInfoByOrderCodeAndUserID
	if getInfoByUserFunc == nil {
		log.Fatal("❌ GetOrderInfoByOrderCodeAndUserID 方法签名错误")
	}
	fmt.Println("    ✅ GetOrderInfoByOrderCodeAndUserID() error")
	
	// 测试批量操作方法
	fmt.Println("  测试批量操作方法:")
	
	// UpdateMapByIds 方法
	var updateMapFunc func([]uint, map[string]interface{}, ...interface{}) error = orderInfo.UpdateMapByIds
	if updateMapFunc == nil {
		log.Fatal("❌ UpdateMapByIds 方法签名错误")
	}
	fmt.Println("    ✅ UpdateMapByIds(ids []uint, update map[string]interface{}, tx ...interface{}) error")
	
	// UpdateMapByOrderCodes 方法
	var updateMapByCodesFunc func([]string, map[string]interface{}, ...interface{}) error = orderInfo.UpdateMapByOrderCodes
	if updateMapByCodesFunc == nil {
		log.Fatal("❌ UpdateMapByOrderCodes 方法签名错误")
	}
	fmt.Println("    ✅ UpdateMapByOrderCodes(orderCodes []string, update map[string]interface{}, tx ...interface{}) error")
	
	// 测试特殊方法
	fmt.Println("  测试特殊方法:")
	
	// PutOrder 方法
	var putOrderFunc func(interface{}) error = orderInfo.PutOrder
	if putOrderFunc == nil {
		log.Fatal("❌ PutOrder 方法签名错误")
	}
	fmt.Println("    ✅ PutOrder(tx interface{}) error")
	
	// OrderWorkOk 方法
	var orderWorkOkFunc func(interface{}) error = orderInfo.OrderWorkOk
	if orderWorkOkFunc == nil {
		log.Fatal("❌ OrderWorkOk 方法签名错误")
	}
	fmt.Println("    ✅ OrderWorkOk(tx interface{}) error")
	
	// QueryByOrderCodeWithLock 方法
	var queryWithLockFunc func(interface{}) error = orderInfo.QueryByOrderCodeWithLock
	if queryWithLockFunc == nil {
		log.Fatal("❌ QueryByOrderCodeWithLock 方法签名错误")
	}
	fmt.Println("    ✅ QueryByOrderCodeWithLock(tx interface{}) error")
}

func testTransactionCompatibility() {
	fmt.Println("\n🔄 测试事务参数兼容性...")
	
	orderInfo := &order.OrderInfo{}
	
	// 测试 interface{} 参数类型
	fmt.Println("  测试参数类型兼容性:")
	
	// 测试 Create 方法可以接受不同类型的参数
	createMethod := reflect.ValueOf(orderInfo.Create)
	createType := createMethod.Type()
	
	// 检查参数类型
	if createType.NumIn() != 1 {
		log.Fatal("❌ Create 方法参数数量错误")
	}
	
	paramType := createType.In(0)
	if paramType.Kind() != reflect.Slice {
		log.Fatal("❌ Create 方法参数类型错误")
	}
	
	elemType := paramType.Elem()
	if elemType.Kind() != reflect.Interface {
		log.Fatal("❌ Create 方法参数元素类型错误")
	}
	
	fmt.Println("    ✅ 方法参数支持 ...interface{} 类型")
	fmt.Println("    ✅ 可以兼容 GORM v1 和 v2 事务参数")
}

func testStructTags() {
	fmt.Println("\n🏷️  测试结构体标签更新...")
	
	orderInfo := &order.OrderInfo{}
	orderType := reflect.TypeOf(orderInfo).Elem()
	
	// 检查主键字段标签
	idField, found := orderType.FieldByName("ID")
	if !found {
		log.Fatal("❌ 找不到 ID 字段")
	}
	
	gormTag := idField.Tag.Get("gorm")
	if gormTag != "primaryKey" {
		log.Fatalf("❌ ID 字段 GORM 标签错误，期望: primaryKey，实际: %s", gormTag)
	}
	fmt.Println("    ✅ ID 字段标签已更新为 GORM v2 格式: primaryKey")
	
	// 检查 OrderCode 字段标签
	orderCodeField, found := orderType.FieldByName("OrderCode")
	if !found {
		log.Fatal("❌ 找不到 OrderCode 字段")
	}
	
	orderCodeGormTag := orderCodeField.Tag.Get("gorm")
	if orderCodeGormTag != "uniqueIndex;not null" {
		log.Fatalf("❌ OrderCode 字段 GORM 标签错误，期望: uniqueIndex;not null，实际: %s", orderCodeGormTag)
	}
	fmt.Println("    ✅ OrderCode 字段标签已更新为 GORM v2 格式: uniqueIndex;not null")
	
	// 检查表名方法
	tableName := orderInfo.TableName()
	if tableName != "order_info" {
		log.Fatalf("❌ 表名错误，期望: order_info，实际: %s", tableName)
	}
	fmt.Println("    ✅ 表名方法正常: order_info")
}

func testConstantsAndTypes() {
	fmt.Println("\n📊 测试常量和类型定义...")
	
	// 测试订单状态常量
	fmt.Println("  测试订单状态常量:")

	if order.NOT_PAY == 1 {
		fmt.Println("    ✅ NOT_PAY 常量正确")
	} else {
		log.Fatalf("❌ NOT_PAY 常量错误，期望: 1，实际: %d", order.NOT_PAY)
	}

	if order.HAVE_PAY == 2 {
		fmt.Println("    ✅ HAVE_PAY 常量正确")
	} else {
		log.Fatalf("❌ HAVE_PAY 常量错误，期望: 2，实际: %d", order.HAVE_PAY)
	}

	// 测试订单类型常量
	fmt.Println("  测试订单类型常量:")

	// 查找 POD_ORDER 常量定义
	fmt.Printf("    ℹ️  POD_ORDER 值: %d\n", order.POD_ORDER)
	fmt.Println("    ✅ POD_ORDER 常量可访问")

	// 查找 ORDER_SHOP_TYPE_SHOPIFY 常量定义
	fmt.Printf("    ℹ️  ORDER_SHOP_TYPE_SHOPIFY 值: %d\n", order.ORDER_SHOP_TYPE_SHOPIFY)
	fmt.Println("    ✅ ORDER_SHOP_TYPE_SHOPIFY 常量可访问")
	
	// 测试结构体类型
	fmt.Println("  测试结构体类型:")
	
	orderInfo := &order.OrderInfo{}
	if orderInfo != nil {
		fmt.Println("    ✅ OrderInfo 结构体类型正确")
	}
	
	priceInfo := order.PriceInfo{}
	if reflect.TypeOf(priceInfo).Name() == "PriceInfo" {
		fmt.Println("    ✅ PriceInfo 结构体类型正确")
	}
	
	stateInfo := order.StateInfo{}
	if reflect.TypeOf(stateInfo).Name() == "StateInfo" {
		fmt.Println("    ✅ StateInfo 结构体类型正确")
	}
}
