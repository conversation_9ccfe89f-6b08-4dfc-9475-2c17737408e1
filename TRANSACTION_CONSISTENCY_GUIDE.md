# GORM v2 迁移事务一致性指南

## ⚠️ 重要问题：事务一致性风险

在 GORM v1 到 v2 迁移过程中，发现了一个严重的事务一致性问题：

### 问题描述

当调用方传入 GORM v1 事务参数时，如果方法内部创建新的 GORM v2 连接，会导致：

1. **事务隔离**：订单和订单子项在不同的事务中执行
2. **原子性破坏**：可能出现订单创建成功但订单子项创建失败
3. **数据不一致**：违反业务逻辑的原子性要求

### 示例场景

```go
// 危险的场景
func CreateOrderWithItems(orderInfo *OrderInfo, items []*OrderItem) error {
    // 开始 GORM v1 事务
    tx := mysql.NewConn().Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    // 创建订单 - 如果内部创建新的 v2 连接，会脱离当前事务
    if err := orderInfo.Create(tx); err != nil {
        tx.Rollback()
        return err
    }
    
    // 创建订单子项 - 在原事务中
    for _, item := range items {
        if err := item.Create(tx); err != nil {
            tx.Rollback()
            return err
        }
    }
    
    return tx.Commit().Error
}
```

在上述场景中，如果 `orderInfo.Create(tx)` 内部创建新的 v2 连接，订单会在新连接中创建，而订单子项在原事务中创建，破坏了原子性。

## 🔧 解决方案

### 方案一：双版本方法（推荐）

为每个重要的事务方法提供两个版本：

```go
// GORM v2 版本（推荐使用，事务安全）
func (o *OrderInfo) CreateV2(tx ...*gorm2.DB) error {
    var dbV2 *gorm2.DB
    if len(tx) != 0 {
        dbV2 = tx[0]  // 使用传入的事务
    } else {
        dbV2 = mysql.NewConnV2()  // 使用默认连接
    }
    // ... 业务逻辑
}

// 兼容版本（保持向后兼容，但有风险）
func (o *OrderInfo) Create(tx ...interface{}) error {
    if len(tx) != 0 {
        switch t := tx[0].(type) {
        case *gorm.DB:
            // 警告：事务一致性风险
            log.Warn("检测到 GORM v1 事务，建议使用 CreateV2() 方法")
            return o.CreateV2()  // 使用默认连接，破坏事务一致性
        case *gorm2.DB:
            return o.CreateV2(t)  // 使用 v2 事务
        }
    }
    return o.CreateV2()
}
```

### 方案二：严格模式（生产环境推荐）

```go
func (o *OrderInfo) Create(tx ...interface{}) error {
    if len(tx) != 0 {
        switch t := tx[0].(type) {
        case *gorm.DB:
            // 直接返回错误，强制调用方升级
            return fmt.Errorf("不支持 GORM v1 事务，请使用 CreateV2() 方法或升级调用代码")
        case *gorm2.DB:
            return o.CreateV2(t)
        }
    }
    return o.CreateV2()
}
```

## 📋 迁移指南

### 1. 立即行动项

对于涉及事务的关键业务逻辑，立即检查并升级：

```go
// ❌ 有风险的代码
func CreateOrderWithItems(orderInfo *OrderInfo, items []*OrderItem) error {
    tx := mysql.NewConn().Begin()  // GORM v1 事务
    
    if err := orderInfo.Create(tx); err != nil {  // 可能破坏事务一致性
        tx.Rollback()
        return err
    }
    
    // ... 创建订单子项
}

// ✅ 安全的代码
func CreateOrderWithItems(orderInfo *OrderInfo, items []*OrderItem) error {
    tx := mysql.NewConnV2().Begin()  // GORM v2 事务
    
    if err := orderInfo.CreateV2(tx); err != nil {  // 事务安全
        tx.Rollback()
        return err
    }
    
    // ... 创建订单子项
}
```

### 2. 渐进式迁移

1. **识别事务代码**：找出所有使用事务的业务逻辑
2. **优先级排序**：优先迁移关键业务流程
3. **逐步升级**：将 GORM v1 事务升级为 v2
4. **测试验证**：确保事务行为正确

### 3. 代码审查清单

- [ ] 是否存在跨 GORM 版本的事务调用？
- [ ] 订单创建是否与订单子项在同一事务中？
- [ ] 是否有其他需要原子性的业务操作？
- [ ] 错误处理是否正确回滚事务？

## 🧪 测试验证

### 事务一致性测试

```go
func TestTransactionConsistency(t *testing.T) {
    // 测试事务回滚是否正确
    tx := mysql.NewConnV2().Begin()
    
    orderInfo := &OrderInfo{
        OrderCode: "TEST-TX-001",
        UserID:    12345,
    }
    
    // 创建订单
    err := orderInfo.CreateV2(tx)
    assert.NoError(t, err)
    
    // 模拟订单子项创建失败
    // ... 
    
    // 回滚事务
    tx.Rollback()
    
    // 验证订单没有被创建
    var count int64
    mysql.NewConnV2().Model(&OrderInfo{}).Where("order_code = ?", "TEST-TX-001").Count(&count)
    assert.Equal(t, int64(0), count, "事务回滚后订单不应该存在")
}
```

## 📊 影响评估

### 高风险场景

1. **订单创建流程**：订单 + 订单子项 + 库存扣减
2. **支付处理**：订单状态更新 + 支付记录 + 通知发送
3. **退款流程**：订单状态 + 退款记录 + 库存恢复
4. **批量操作**：多个相关实体的批量更新

### 中风险场景

1. **单一实体操作**：只涉及一个表的操作
2. **查询操作**：只读操作，不涉及事务
3. **日志记录**：非关键业务数据

## 🚀 推荐实践

### 1. 新代码

```go
// 推荐：直接使用 GORM v2
func CreateOrder(orderInfo *OrderInfo) error {
    tx := mysql.NewConnV2().Begin()
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    if err := orderInfo.CreateV2(tx); err != nil {
        tx.Rollback()
        return err
    }
    
    return tx.Commit().Error
}
```

### 2. 现有代码迁移

```go
// 第一步：保持接口不变，内部升级
func CreateOrder(orderInfo *OrderInfo) error {
    // 将 GORM v1 事务升级为 v2
    tx := mysql.NewConnV2().Begin()  // 改这里
    defer func() {
        if r := recover(); r != nil {
            tx.Rollback()
        }
    }()
    
    if err := orderInfo.CreateV2(tx); err != nil {  // 改这里
        tx.Rollback()
        return err
    }
    
    return tx.Commit().Error
}
```

## ⚡ 总结

事务一致性是数据库操作的核心要求。在 GORM v1 到 v2 迁移过程中，必须特别注意：

1. **不要混用**：避免在同一个事务流程中混用 v1 和 v2
2. **优先 v2**：新代码直接使用 GORM v2
3. **渐进迁移**：现有代码逐步升级事务处理
4. **充分测试**：确保事务行为符合预期

通过正确的迁移策略，可以在保证系统稳定性的同时，逐步享受 GORM v2 的优势。
